import _ from 'lodash';
import {trim} from 'framework/helpers';
import {handleIntegrationError, ParkPaletClient} from './utils';
import {normalizeAddress} from '../../../../store/api/checkout/approve-payment';

const shippingStatusMap = {
    DeliveredToCargo: 'order-created',
    ProcessedByCargo: 'shipment-prepared',
    InTransit: 'in-transfer-phase',
    DeliveredToReceiver: 'delivered',
    NotDelivered: 'failed-to-deliver'
};

const integrationStatuses = [
    {value: 'CargoCreateFailed', label: 'Cargo Creation Failed'},
    {value: 'ProductMatchingFailed', label: 'Product Matching Failed'},
    {value: 'WarehouseDecisionFailed', label: 'Warehouse Decision Failed'},
    {value: 'WarehouseSendingFailed', label: 'Warehouse Sending Failed'},
    {value: 'SentToWarehouse', label: 'Sent to Warehouse'},
    {value: 'Dispatchable', label: 'Ready for Dispatch'},
    {value: 'Waiting', label: 'Waiting'},
    {value: 'PendingStock', label: 'Pending Stock'},
    {value: 'Packed', label: 'Packed'},
    {value: 'DeliveredToCargo', label: 'Delivered to Cargo'},
    {value: 'Shipped', label: 'Shipped'},
    {value: 'ProcessedByCargo', label: 'Processed by Cargo'},
    {value: 'InTransit', label: 'In Transit'},
    {value: 'DeliveredToReceiver', label: 'Delivered to Receiver'},
    {value: 'Delivered', label: 'Delivered to Receiver'},
    {value: 'NotDelivered', label: 'Not Delivered'},
    {value: 'Cancelled', label: 'Cancelled'},
    {value: 'Completed', label: 'Completed'},
    {value: 'InProgress', label: 'In Progress'},
    {value: 'Error', label: 'Error'}
];

export default async function updateIntegrationTransfer(app, integration, documentId, orderId, payload) {
    try {
        const now = app.datetime.local();

        // ParkPalet's API is offline between these hours.
        if (now.hour >= 20 || now.hour < 8) {
            return;
        }

        if (!orderId || !_.isPlainObject(payload)) {
            throw new Error('Invalid order or payload.');
        }

        const client = await ParkPaletClient(app, integration.integrationParams);

        if (payload.orderType === 'external-marketplace-order') {
            const orders = (await client.get(`/orders/v1?filterParameters.searchFilter=${orderId}`)).data;

            if (orders && Array.isArray(orders.results) && orders.results.length > 0) {
                const order = orders.results.find(order => order.orderNumber.split('-')?.[1] === orderId);

                if (order) {
                    const activeStatus = integrationStatuses.find(status => status.value === order.orderStatus);
                    if (activeStatus) {
                        const transfer = await app.collection('inventory.transfers').findOne({
                            _id: documentId,
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });
                        const transactions = transfer.integrationPayload?.transactions || [];

                        if (!transactions.some(t => t.value === activeStatus.value)) {
                            transactions.push({
                                value: activeStatus.value,
                                label: activeStatus.label,
                                date: now.toJSDate()
                            });
                        }

                            try {
                                await app.rpc('logistics.sync-document-shipment-status', {
                                    transferId: documentId,
                                    status: shippingStatusMap[activeStatus.value] || 'order-created',
                                    transactionDate: now.toJSDate()
                                });
                            } catch (error) {
                                console.log('Parkpalet external order shipment status sync problem ->', error.message);
                            }

                        const isOrderCompleted = ['DeliveredToReceiver', 'Delivered', 'NotDelivered'].includes(
                            activeStatus.value
                        );
                        const isOrderCanceled = activeStatus.value === 'Cancelled';
                        const isOrderFailed = [
                            'CargoCreateFailed',
                            'ProductMatchingFailed',
                            'WarehouseDecisionFailed',
                            'WarehouseSendingFailed',
                            'Error'
                        ].includes(activeStatus.value);

                        const deliveredDate = ['DeliveredToReceiver', 'Delivered'].includes(activeStatus.value)
                            ? now.toJSDate()
                            : null;

                        await app.collection('inventory.transfers').bulkWrite([
                            {
                                updateOne: {
                                    filter: {_id: documentId},
                                    update: {
                                        $set: {
                                            ...(isOrderCompleted && {'integrationPayload.status': 'completed'}),
                                            ...(isOrderFailed && {'integrationPayload.status': 'failed'}),
                                            ...((isOrderCanceled || transfer.status === 'canceled') && {
                                                'integrationPayload.status': 'canceled'
                                            }),
                                            ...(order.trackingNumber && {cargoTrackingCode: order.trackingNumber}),
                                            ...(!payload.deliveredDate && {
                                                'integrationPayload.deliveredDate': deliveredDate
                                            }),
                                            'integrationPayload.transactions': transactions
                                        }
                                    }
                                }
                            }
                        ]);

                        const shouldApproveTransfer = ['ProcessedByCargo', 'InTransit', 'DeliveredToReceiver'].includes(
                            activeStatus.value
                        );

                        if (shouldApproveTransfer && transfer.status !== 'approved' && transfer.status !== 'canceled') {
                            const user = await app.collection('kernel.users').findOne({isRoot: true});

                            await app.rpc(
                                'inventory.transfers-save-transfer',
                                {
                                    data: {
                                        ..._.omit(transfer, '_id'),
                                        shippingDocumentType: 'none',
                                        status: 'approved'
                                    },
                                    id: transfer._id
                                },
                                {user}
                            );
                        }

                        if (isOrderCanceled) {
                            const user = await app.collection('kernel.users').findOne({isRoot: true});
                            await app.rpc('inventory.cancel-transfer', documentId, {user});
                        }
                    }
                }
            }
        }

        if (payload.orderType === 'sale-order') {
            const order = (await client.get(`/orders/v1/${orderId}`)).data;

            if (!_.isPlainObject(order)) {
                throw new Error(`Order not found with the following order id: ${orderId}`);
            }

            const activeStatus = integrationStatuses.find(status => status.value === order.orderStatus);
            if (activeStatus) {
                const transfer = await app.collection('inventory.transfers').findOne({
                    _id: documentId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const transactions = transfer.integrationPayload?.transactions || [];

                if (!transactions.some(t => t.value === activeStatus.value)) {
                    transactions.push({
                        value: activeStatus.value,
                        label: activeStatus.label,
                        date: now.toJSDate()
                    });
                }

                const shippingStatus = shippingStatusMap[activeStatus.value];

                    try {
                        await app.rpc('logistics.sync-document-shipment-status', {
                            transferId: documentId,
                            status: shippingStatus || 'order-created',
                            transactionDate: now.toJSDate()
                        });
                    } catch (error) {
                        console.log('Parkpalet integration order shipment status sync problem ->', error.message);
                    }

                const isDeliveredToCargo = ['ProcessedByCargo', 'InTransit'].includes(activeStatus.value);
                const isDeliveredToReceiver = activeStatus.value === 'DeliveredToReceiver';
                const isOrderCompleted = ['DeliveredToReceiver', 'NotDelivered'].includes(activeStatus.value);
                const isOrderCanceled = activeStatus.value === 'Cancelled';
                const isOrderFailed = [
                    'CargoCreateFailed',
                    'ProductMatchingFailed',
                    'WarehouseDecisionFailed',
                    'WarehouseSendingFailed'
                ].includes(activeStatus.value);

                const deliveredDate = activeStatus.value === 'DeliveredToReceiver' ? now.toJSDate() : null;
                const canBeCanceled = order.flags?.isCancellable ?? null;

                let isShipmentMailSent = transfer.integrationPayload?.isShipmentMailSent ?? false;
                if (isDeliveredToCargo && !isShipmentMailSent) {
                    try {
                        await sendShipmentMail(
                            app,
                            documentId,
                            transfer.integrationPayload?.shipmentTrackingCode,
                            transfer.integrationPayload?.shipmentTrackingUrl,
                            order.cargoInfo?.firmName
                        );
                        isShipmentMailSent = true;
                    } catch (error) {
                        console.log('Parkpalet shipment mail error ->', error);
                    }
                }

                let isDeliveredMailSent = transfer.integrationPayload?.isDeliveredMailSent ?? false;
                if (isDeliveredToReceiver && !isDeliveredMailSent) {
                    try {
                        await sendDeliveredMail(app, documentId, deliveredDate);
                        isDeliveredMailSent = true;
                    } catch (error) {
                        console.log('Parkpalet delivered mail error ->', error);
                    }
                }

                await app.collection('inventory.transfers').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: documentId},
                            update: {
                                $set: {
                                    ...(isOrderCompleted && {'integrationPayload.status': 'completed'}),
                                    ...(isOrderFailed && {'integrationPayload.status': 'failed'}),
                                    ...((isOrderCanceled || transfer.status === 'canceled') && {
                                        'integrationPayload.status': 'canceled'
                                    }),
                                    ...(shippingStatus && {'integrationPayload.shippingStatus': shippingStatus}),
                                    ...(order.cargoInfo?.trackingNumber && {
                                        'integrationPayload.shipmentTrackingCode': order.cargoInfo.trackingNumber,
                                        cargoTrackingCode: order.cargoInfo.trackingNumber
                                    }),
                                    ...(order.cargoInfo?.trackingUrl && {
                                        'integrationPayload.shipmentTrackingUrl': order.cargoInfo.trackingUrl
                                    }),
                                    ...(!payload.deliveredDate && {
                                        'integrationPayload.deliveredDate': deliveredDate
                                    }),
                                    ...(isShipmentMailSent && {'integrationPayload.isShipmentMailSent': true}),
                                    ...(isDeliveredMailSent && {'integrationPayload.isDeliveredMailSent': true}),
                                    'integrationPayload.canBeCanceled': canBeCanceled,
                                    'integrationPayload.transactions': transactions
                                }
                            }
                        }
                    }
                ]);

                const shouldApproveTransfer = ['ProcessedByCargo', 'InTransit', 'DeliveredToReceiver'].includes(
                    activeStatus.value
                );

                if (shouldApproveTransfer && transfer.status !== 'approved' && transfer.status !== 'canceled') {
                    const user = await app.collection('kernel.users').findOne({isRoot: true});

                    await app.rpc(
                        'inventory.transfers-save-transfer',
                        {
                            data: {
                                ..._.omit(transfer, '_id'),
                                shippingDocumentType: 'none',
                                status: 'approved'
                            },
                            id: transfer._id
                        },
                        {user}
                    );
                }

                if (isOrderCanceled) {
                    const user = await app.collection('kernel.users').findOne({isRoot: true});
                    await app.rpc('inventory.cancel-transfer', documentId, {user});
                }
            }
        }

        if (payload.orderType === 'purchase-order') {
            const order = (await client.get(`/purchaseorders/v1/${orderId}`)).data;

            if (!_.isPlainObject(order)) {
                throw new Error(`Order not found with the following order id: ${orderId}`);
            }

            const activeStatus = integrationStatuses.find(status => status.value === order.orderStatus);
            if (activeStatus) {
                const transfer = await app.collection('inventory.transfers').findOne({
                    _id: documentId,
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const transactions = transfer.integrationPayload?.transactions || [];

                if (!transactions.some(t => t.value === activeStatus.value)) {
                    transactions.push({
                        value: activeStatus.value,
                        label: activeStatus.label,
                        date: now.toJSDate()
                    });
                }

                const isOrderCompleted = activeStatus.value === 'Completed';
                const isOrderCanceled = activeStatus.value === 'Cancelled';
                const isOrderFailed = activeStatus.value === 'Error';

                await app.collection('inventory.transfers').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: documentId},
                            update: {
                                $set: {
                                    ...(isOrderCompleted && {'integrationPayload.status': 'completed'}),
                                    ...(isOrderFailed && {'integrationPayload.status': 'failed'}),
                                    ...(isOrderCanceled && {'integrationPayload.status': 'canceled'}),
                                    'integrationPayload.transactions': transactions
                                }
                            }
                        }
                    }
                ]);

                if (isOrderCanceled) {
                    const user = await app.collection('kernel.users').findOne({isRoot: true});
                    await app.rpc('inventory.cancel-transfer', documentId, {user});
                }
            }
        }
    } catch (error) {
        handleIntegrationError({
            app,
            error,
            documentId: integration._id,
            documentIdentifier: integration.code,
            referenceId: documentId,
            shouldLog: true
        });
    }
}

async function sendShipmentMail(app, documentId, shipmentTrackingCode, shipmentTrackingUrl, carrierName) {
    const transfer = await app.collection('inventory.transfers').findOne({
        _id: documentId,
        $select: ['referenceId', 'referenceCollection', 'integrationPayload'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });

    if (!transfer || !transfer.referenceId || transfer.referenceCollection !== 'sale.orders') return;

    if (transfer.integrationPayload?.isShipmentMailSent) return;

    const order = await app.collection('sale.orders').findOne({
        _id: transfer.referenceId,
        $select: ['_id', 'code', 'storeId'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });

    if (!order) return;

    const store = await app.collection('ecommerce.stores').findOne({
        _id: order.storeId,
        $select: ['website', 'name', 'email'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const cart = await app.collection('store.cart').findOne({
        orderId: order._id
    });
    const company = await app.collection('kernel.company').findOne({});

    if (!store || !cart) return;

    const t = text => app.translate(text);
    const siteUrl = trim(store.website, '/');
    const deliveryAddress = await normalizeAddress(app, cart.deliveryAddress);
    const billingAddress = await normalizeAddress(app, cart.billingAddress);

    const template = `
<mj-wrapper full-width
            background-color="#FFEDD6"
            padding="24px"
            css-class="content-wrapper">
    <mj-section padding-bottom="48px" padding-top="32px">
        <mj-column>
            <mj-image align="center" src="${app.absoluteUrl('static/images/mail/order-shipped.png')}" width="150px"/>
        </mj-column>
    </mj-section>

    <mj-section>
        <mj-column>
            <mj-text font-size="21px"
                     color="#FA730F"
                     font-weight="600" padding="0" padding-bottom="12px">${t('Your Package Has Been Shipped')}</mj-text>
            <mj-text>${t(
                'You can find information about the delivery process of your package on the "My Orders" page on our website.'
            )}</mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding-top="10px">
        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('ORDER NUMBER')}</mj-text>
            <mj-text font-size="13px" padding="0">${order.code}</mj-text>
        </mj-column>

        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('TOTAL AMOUNT')}</mj-text>
            <mj-text font-size="13px" padding="0">${app.format(cart.grandTotal || 0, 'currency')}</mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding-top="10px">
        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('DELIVERY ADDRESS')}</mj-text>
            <mj-text font-size="13px" padding="0">${deliveryAddress.address}</mj-text>
        </mj-column>

         ${
             _.isEmpty(carrierName)
                 ? ''
                 : `
        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('CARGO COMPANY')}</mj-text>
            <mj-text font-size="13px" padding="0">${carrierName}</mj-text>
        </mj-column>
        `
         }
    </mj-section>

    <mj-section padding-top="10px">
         ${
             _.isEmpty(shipmentTrackingCode)
                 ? ''
                 : `
        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('CARGO TRACKING CODE')}</mj-text>
            <mj-text font-size="13px" padding="0">${shipmentTrackingCode}</mj-text>
        </mj-column>
        `
         }
    </mj-section>
     
    ${
        _.isEmpty(shipmentTrackingUrl)
            ? ''
            : `
        <mj-section padding-top="10px">
        <mj-column>
        <mj-button background-color="#FA730F" color="white" align="center" width="100%" border-radius="8px" font-weight="600" href=${shipmentTrackingUrl}>
            ${t('Shipment Tracking Link')}
        </mj-button>
        </mj-column>
        </mj-section>
    `
    }
</mj-wrapper>

<mj-section padding-top="48px" padding-bottom="16px">
    <mj-column>
        <mj-text padding="0" font-size="18px" font-weight="600">${t('Order Summary')}</mj-text>
    </mj-column>
</mj-section>
<mj-wrapper background-color="white"
            padding-left="24px"
            padding-right="24px"
            padding-bottom="12px"
            padding-top="24px"
            css-class="content-wrapper-with-border">
${cart.items
    .map(item => {
        return `
<mj-section>
    <mj-column width="15%">
        <mj-image padding="0" padding-right="32px" padding-bottom="12px" src="${item.productImage}.png"/>
    </mj-column>

    <mj-column width="85%">
        <mj-text font-size="15px" padding="0">
            <a href="${!!item.productLink ? `${siteUrl}${item.productLink}` : `${siteUrl}/${item.productSlug}`}">
                ${item.productName}
            </a>
        </mj-text>
        <mj-text padding="0" padding-top="8px" font-size="14px" font-weight="500">
            ${item.quantity} x ${app.format(item.price || 0, 'currency')}
        </mj-text>
    </mj-column>
</mj-section>

<mj-section>
    <mj-column>
        <mj-divider padding-top="16px" padding-bottom="16px"/>
    </mj-column>
</mj-section>
`;
    })
    .join('\n')}

    <mj-section>
       <mj-group>
           <mj-column>
               <mj-text font-size="14px" padding="0" line-height="21px">
                   ${t('Products Total')} (${cart.productCount})
               </mj-text>
           </mj-column>

           <mj-column>
               <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                   ${app.format(cart.subTotal || 0, 'currency')}
               </mj-text>
           </mj-column>
       </mj-group>
    </mj-section>
    <mj-section padding-top="4px">
        <mj-group>
            <mj-column>
                <mj-text font-size="14px" padding="0" line-height="21px">
                    ${t('Tax total')}
                </mj-text>
            </mj-column>

            <mj-column>
                <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                      ${app.format(cart.taxTotal || 0, 'currency')}
                </mj-text>
            </mj-column>
        </mj-group>
    </mj-section>
    <mj-section padding-top="4px">
        <mj-group>
            <mj-column>
                <mj-text font-size="14px" padding="0" line-height="21px">
                    ${t('Delivery amount')}
                </mj-text>
            </mj-column>

            <mj-column>
                <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                      ${app.format(cart.deliveryTotal || 0, 'currency')}
                </mj-text>
            </mj-column>
        </mj-group>
    </mj-section>
    ${
        (cart.cashOnDeliveryServiceFee || 0) > 0
            ? `
<mj-section padding-top="4px">
    <mj-group>
        <mj-column>
            <mj-text font-size="14px" padding="0" line-height="21px">
                ${t('Cash on delivery service fee')}
            </mj-text>
        </mj-column>

        <mj-column>
            <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                ${app.format(cart.cashOnDeliveryServiceFee || 0, 'currency')}
            </mj-text>
        </mj-column>
    </mj-group>
</mj-section>
    `
            : ``
    }
    ${
        (cart.dueDifference || 0) > 0
            ? `
<mj-section padding-top="4px">
    <mj-group>
        <mj-column>
            <mj-text font-size="14px" padding="0" line-height="21px">
                ${t('Due difference amount')}
            </mj-text>
        </mj-column>

        <mj-column>
            <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                ${app.format(cart.dueDifference || 0, 'currency')}
            </mj-text>
        </mj-column>
    </mj-group>
</mj-section>
    `
            : ``
    }
        ${
            Array.isArray(cart.discounts) && cart.discounts.length > 0
                ? cart.discounts
                      .map(
                          discount => `
<mj-section padding-top="4px">
    <mj-group>
        <mj-column>
            <mj-text font-size="14px" padding="0" line-height="21px">
                ${discount.description}
            </mj-text>
        </mj-column>

        <mj-column>
            <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                -${app.format(discount.amount || 0, 'currency')}
            </mj-text>
        </mj-column>
    </mj-group>
</mj-section>
    `
                      )
                      .join('\n')
                : ``
        }
    <mj-section padding-top="8px">
       <mj-group>
           <mj-column>
               <mj-text font-size="16px" padding="0" font-weight="600" line-height="21px">
                    ${t('Grand total')}
               </mj-text>
           </mj-column>

           <mj-column>
               <mj-text align="right" font-weight="600" font-size="16px" padding="0" line-height="21px">
                    ${app.format(cart.grandTotal || 0, 'currency')}
               </mj-text>
           </mj-column>
       </mj-group>
    </mj-section>

    <mj-section padding-top="16px">
        <mj-column>
            <mj-divider padding-top="16px" padding-bottom="16px"/>
        </mj-column>
    </mj-section>
    <mj-section padding-top="0">
        <mj-column>
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Payment Information'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">
               ${
                   cart.installmentCount > 1
                       ? app.translate('{{installmentCount}} installments', {installmentCount: cart.installmentCount})
                       : t('Single installment')
               }
            </mj-text>
        </mj-column>
    </mj-section>

     <mj-section padding-top="16px">
        <mj-column>
            <mj-divider padding-top="16px" padding-bottom="16px"/>
        </mj-column>
    </mj-section>
    <mj-section padding-top="0">
        <mj-column padding-bottom="12px">
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Delivery Address'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" padding-bottom="4px" line-height="21px">
                ${cart.firstName} ${cart.lastName}
            </mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">${deliveryAddress.address}</mj-text>
        </mj-column>

        <mj-column padding-bottom="12px">

        </mj-column>

        <mj-column padding-bottom="12px">
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Invoice Information'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" padding-bottom="4px" line-height="21px">
                ${!!cart.companyName ? cart.companyName : `${cart.firstName} ${cart.lastName}`}
            </mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">${billingAddress.address}</mj-text>
        </mj-column>
    </mj-section>
</mj-wrapper>
    `;

    await app.mail({
        from: `${store.name} <${store.email || company.email}>`,
        to: cart.email,
        subject: `${app.translate('Your Package Has Been Shipped')}🚚`,
        noContentWrapper: true,
        template
    });
}

async function sendDeliveredMail(app, documentId, deliveryDate) {
    const transfer = await app.collection('inventory.transfers').findOne({
        _id: documentId,
        $select: ['referenceId', 'referenceCollection', 'integrationPayload'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });

    if (!transfer || !transfer.referenceId || transfer.referenceCollection !== 'sale.orders') return;

    if (transfer.integrationPayload?.isDeliveredMailSent) return;

    const order = await app.collection('sale.orders').findOne({
        _id: transfer.referenceId,
        $select: ['_id', 'code', 'storeId'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });

    if (!order) return;

    const store = await app.collection('ecommerce.stores').findOne({
        _id: order.storeId,
        $select: ['website', 'name', 'email'],
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    const cart = await app.collection('store.cart').findOne({
        orderId: order._id
    });
    const company = await app.collection('kernel.company').findOne({});

    if (!store || !cart) return;

    const t = text => app.translate(text);
    const siteUrl = trim(store.website, '/');
    const deliveryAddress = await normalizeAddress(app, cart.deliveryAddress);
    const billingAddress = await normalizeAddress(app, cart.billingAddress);

    deliveryDate = _.isDate(deliveryDate) ? app.format(deliveryDate, 'date') : '---';

    const template = `
<mj-wrapper full-width
            background-color="#BDF8D1"
            padding="24px"
            css-class="content-wrapper">
    <mj-section padding-bottom="48px" padding-top="32px">
        <mj-column>
            <mj-image align="center" src="${app.absoluteUrl('static/images/mail/order-delivered.png')}" width="150px"/>
        </mj-column>
    </mj-section>

    <mj-section>
        <mj-column>
            <mj-text font-size="21px"
                     color="#16A34A"
                     font-weight="600" padding="0" padding-bottom="12px">${t(
                         'Your Package Has Been Delivered'
                     )}</mj-text>
            <mj-text>${t(
                'You can find information about the delivery process of your package on the "My Orders" page on our website.'
            )}</mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding-top="16px">
        <mj-column padding-bottom="12px">
            <mj-text font-size="13px" font-weight="600" padding="0">${t('ORDER NUMBER')}</mj-text>
            <mj-text font-size="13px" padding="0">${order.code}</mj-text>
        </mj-column>

        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('TOTAL AMOUNT')}</mj-text>
            <mj-text font-size="13px" padding="0">${app.format(cart.grandTotal || 0, 'currency')}</mj-text>
        </mj-column>
    </mj-section>

    <mj-section padding-top="12px">
        <mj-column>
            <mj-text font-size="13px" font-weight="600" padding="0">${t('DELIVERY ADDRESS')}</mj-text>
            <mj-text font-size="13px" padding="0">${deliveryAddress.address}</mj-text>
        </mj-column>
    </mj-section>
</mj-wrapper>

<mj-section padding-top="48px" padding-bottom="16px">
    <mj-column>
        <mj-text padding="0" font-size="18px" font-weight="600">${t('Order Summary')}</mj-text>
    </mj-column>
</mj-section>
<mj-wrapper background-color="white"
            padding-left="24px"
            padding-right="24px"
            padding-bottom="12px"
            padding-top="24px"
            css-class="content-wrapper-with-border">
${cart.items
    .map(item => {
        return `
<mj-section>
    <mj-column width="15%">
        <mj-image padding="0" padding-right="32px" padding-bottom="12px" src="${item.productImage}.png"/>
    </mj-column>

    <mj-column width="85%">
        <mj-text font-size="15px" padding="0">
            <a href="${!!item.productLink ? `${siteUrl}${item.productLink}` : `${siteUrl}/${item.productSlug}`}">
                ${item.productName}
            </a>
        </mj-text>
        <mj-text padding="0" padding-top="8px" font-size="14px" font-weight="500">
            ${item.quantity} x ${app.format(item.price || 0, 'currency')}
        </mj-text>
    </mj-column>
</mj-section>

<mj-section>
    <mj-column>
        <mj-divider padding-top="16px" padding-bottom="16px"/>
    </mj-column>
</mj-section>
`;
    })
    .join('\n')}

    <mj-section>
       <mj-group>
           <mj-column>
               <mj-text font-size="14px" padding="0" line-height="21px">
                   ${t('Products Total')} (${cart.productCount})
               </mj-text>
           </mj-column>

           <mj-column>
               <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                   ${app.format(cart.subTotal || 0, 'currency')}
               </mj-text>
           </mj-column>
       </mj-group>
    </mj-section>
    <mj-section padding-top="4px">
        <mj-group>
            <mj-column>
                <mj-text font-size="14px" padding="0" line-height="21px">
                    ${t('Tax total')}
                </mj-text>
            </mj-column>

            <mj-column>
                <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                      ${app.format(cart.taxTotal || 0, 'currency')}
                </mj-text>
            </mj-column>
        </mj-group>
    </mj-section>
    <mj-section padding-top="4px">
        <mj-group>
            <mj-column>
                <mj-text font-size="14px" padding="0" line-height="21px">
                    ${t('Delivery amount')}
                </mj-text>
            </mj-column>

            <mj-column>
                <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                      ${app.format(cart.deliveryTotal || 0, 'currency')}
                </mj-text>
            </mj-column>
        </mj-group>
    </mj-section>
    ${
        (cart.cashOnDeliveryServiceFee || 0) > 0
            ? `
<mj-section padding-top="4px">
    <mj-group>
        <mj-column>
            <mj-text font-size="14px" padding="0" line-height="21px">
                ${t('Cash on delivery service fee')}
            </mj-text>
        </mj-column>

        <mj-column>
            <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                ${app.format(cart.cashOnDeliveryServiceFee || 0, 'currency')}
            </mj-text>
        </mj-column>
    </mj-group>
</mj-section>
    `
            : ``
    }
    ${
        (cart.dueDifference || 0) > 0
            ? `
<mj-section padding-top="4px">
    <mj-group>
        <mj-column>
            <mj-text font-size="14px" padding="0" line-height="21px">
                ${t('Due difference amount')}
            </mj-text>
        </mj-column>

        <mj-column>
            <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                ${app.format(cart.dueDifference || 0, 'currency')}
            </mj-text>
        </mj-column>
    </mj-group>
</mj-section>
    `
            : ``
    }
        ${
            Array.isArray(cart.discounts) && cart.discounts.length > 0
                ? cart.discounts
                      .map(
                          discount => `
<mj-section padding-top="4px">
    <mj-group>
        <mj-column>
            <mj-text font-size="14px" padding="0" line-height="21px">
                ${discount.description}
            </mj-text>
        </mj-column>

        <mj-column>
            <mj-text align="right" font-size="14px" padding="0" line-height="21px">
                -${app.format(discount.amount || 0, 'currency')}
            </mj-text>
        </mj-column>
    </mj-group>
</mj-section>
    `
                      )
                      .join('\n')
                : ``
        }
    <mj-section padding-top="8px">
       <mj-group>
           <mj-column>
               <mj-text font-size="16px" padding="0" font-weight="600" line-height="21px">
                    ${t('Grand total')}
               </mj-text>
           </mj-column>

           <mj-column>
               <mj-text align="right" font-weight="600" font-size="16px" padding="0" line-height="21px">
                    ${app.format(cart.grandTotal || 0, 'currency')}
               </mj-text>
           </mj-column>
       </mj-group>
    </mj-section>

    <mj-section padding-top="16px">
        <mj-column>
            <mj-divider padding-top="16px" padding-bottom="16px"/>
        </mj-column>
    </mj-section>
    <mj-section padding-top="0">
        <mj-column>
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Delivered Date'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">
                ${deliveryDate}
            </mj-text>
        </mj-column>

        <mj-column>

        </mj-column>

        <mj-column>
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Payment Information'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">
               ${
                   cart.installmentCount > 1
                       ? app.translate('{{installmentCount}} installments', {installmentCount: cart.installmentCount})
                       : t('Single installment')
               }
            </mj-text>
        </mj-column>
    </mj-section>

     <mj-section padding-top="16px">
        <mj-column>
            <mj-divider padding-top="16px" padding-bottom="16px"/>
        </mj-column>
    </mj-section>
    <mj-section padding-top="0">
        <mj-column padding-bottom="12px">
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Delivery Address'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" padding-bottom="4px" line-height="21px">
                ${cart.firstName} ${cart.lastName}
            </mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">${deliveryAddress.address}</mj-text>
        </mj-column>

        <mj-column padding-bottom="12px">

        </mj-column>

        <mj-column padding-bottom="12px">
            <mj-text font-size="14px" font-weight="600" padding="0"  padding-bottom="8px">${t(
                'Invoice Information'
            )}</mj-text>
            <mj-text font-size="13px" padding="0" padding-bottom="4px" line-height="21px">
                ${!!cart.companyName ? cart.companyName : `${cart.firstName} ${cart.lastName}`}
            </mj-text>
            <mj-text font-size="13px" padding="0" line-height="21px">${billingAddress.address}</mj-text>
        </mj-column>
    </mj-section>
</mj-wrapper>
    `;

    await app.mail({
        from: `${store.name} <${store.email || company.email}>`,
        to: cart.email,
        subject: `${app.translate('Your Package Has Been Delivered')}📦`,
        noContentWrapper: true,
        template
    });
}
