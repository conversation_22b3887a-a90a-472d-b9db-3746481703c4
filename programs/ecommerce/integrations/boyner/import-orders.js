import _ from 'lodash';
import {template, trim, isArabic, arabicToEnName, toUpper} from 'framework/helpers';
import Random from 'framework/random';
import {apiRequest} from './utils';
import ecommerceIntegrations from '../index';
import microtime from 'microtime';
import {updateProductsSalesCount} from '../utils';

export default async function (app, store, onProgress, startDate, endDate) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };
    const now = app.datetime.local();
    const user = await app.collection('kernel.users').findOne({isRoot: true});
    const company = await app.collection('kernel.company').findOne({});
    const integrationCarriers = await app.rpc('ecommerce.get-carriers', {storeId: store._id});

    let page = 0;
    let currentCount = 0;
    let totalCount = 0;

    await (async function iterator() {
        let start = app.datetime.local().minus({days: 5}).startOf('day').toJSDate().getTime();
        let end = app.datetime.local().endOf('day').toJSDate().getTime();

        if (_.isDate(startDate) && _.isDate(endDate)) {
            start = app.datetime.fromJSDate(startDate).startOf('day').toJSDate().getTime();
            end = app.datetime.fromJSDate(endDate).endOf('day').toJSDate().getTime();
        }

        const result = await apiRequest({
            url,
            path: `sapigw/suppliers/${supplierId}/orders?page=${page}&orderByField=PackageLastModifiedDate&orderByDirection=DESC&size=50&startDate=${start}&endDate=${end}`,
            supplierId,
            apiKey,
            apiSecret
        });

        totalCount = result.totalElements;

        if (Array.isArray(result.content) && result.content.length > 0) {
            for (const integrationOrder of result.content) {
                try {
                    const code = `${store.code}/${integrationOrder.orderNumber}`;
                    const existingOrderCount = await app.collection('sale.orders').count({code});
                    if (existingOrderCount > 0) {
                        continue;
                    }

                    // Get customer, deliveryAddressId, deliveryAddress, invoiceAddressId, invoiceAddress
                    const {customer, invoiceAddressId, invoiceAddress, deliveryAddressId, deliveryAddress} =
                        await getCustomer(app, company, user, store, integrationOrder);

                    try {
                        // Get order.
                        await getOrder({
                            app,
                            company,
                            store,
                            now,
                            user,
                            integrationOrder,
                            customer,
                            invoiceAddressId,
                            invoiceAddress,
                            deliveryAddressId,
                            deliveryAddress,
                            integrationCarriers
                        });
                    } catch (error) {
                        await app.collection('kernel.partners').remove({_id: customer._id}, {disableSoftDelete: true});
                        await app
                            .collection('kernel.contacts')
                            .remove({partnerId: customer._id}, {disableSoftDelete: true});

                        throw error;
                    }
                } catch (error) {
                    log({
                        level: 'error',
                        message: `${app.translate('An error occurred while importing order {{orderNumber}}!', {
                            orderNumber: integrationOrder.orderNumber
                        })} ${error.message}`
                    });
                }
            }

            page++;
            currentCount += result.content.length;

            if (!!onProgress && totalCount > 0) {
                onProgress((currentCount / totalCount) * 100);
            }

            await iterator();
        } else if (!!onProgress) {
            onProgress(100);
        }
    })();
}

async function getOrder({
    app,
    company,
    store,
    now,
    user,
    integrationOrder,
    customer,
    invoiceAddressId,
    invoiceAddress,
    deliveryAddressId,
    deliveryAddress,
    integrationCarriers
}) {
    const paymentTerm = await app.collection('finance.payment-terms').findOne({
        system: true,
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });
    let order = {};

    // General.
    order.module = 'ecommerce';
    order.storeId = store._id;
    order.status = store.automaticOrderApproval ? 'approved' : 'draft';


    order.code = `${store.code}/${integrationOrder.orderNumber}`;
    order.documentTypeId = store.salesDocumentTypeId;
    order.partnerId = customer._id;
    order.partnerGroupId = customer.groupId;
    order.branchId = store.branchId;
    order.currencyId = store.currencyId;
    order.currencyRate = 1;
    order.recordDate = now.toJSDate();
    order.orderDate = now.toJSDate();
    order.scheduledDate = now.toJSDate();
    order.subTotal = 0;
    order.discount = 0;
    order.discountAmount = 0;
    order.subTotalAfterDiscount = 0;
    order.taxTotal = 0;
    order.rounding = 0;
    order.grandTotal = 0;
    order.paidTotal = 0;
    order.plannedTotal = 0;
    order.appliedTaxes = [];
    order.sourceId = store.sourceId;
    order.communicationChannelId = store.communicationChannelId;
    order.invoiceAddressId = invoiceAddressId;
    order.invoiceAddress = invoiceAddress;
    order.deliveryAddressId = deliveryAddressId;
    order.deliveryAddress = deliveryAddress;
    order.note = '';
    order.cargoTrackingCode = integrationOrder.cargoTrackingNumber ||
                              integrationOrder.cargoSenderNumber ||
                              integrationOrder.packageNumber ||
                              '';
    order.warehouseId = store.warehouseId;
    order.paymentTermId = paymentTerm._id;
    order.paymentTerm = paymentTerm;
    order.priceListId = store.priceListId || '';

    // Integration payload.
    const integrationPayload = {};
    integrationPayload.packageId = integrationOrder.id;
    integrationPayload.lines = (integrationOrder.lines ?? []).map(line => ({
        lineId: parseInt(line.id),
        barcode: line.barcode,
        quantity: parseInt(line.quantity)
    }));
    order.integrationPayload = integrationPayload;

    // Delivery note.
    let deliveryNotes = [];
    if (!!integrationOrder.cargoProviderName) {
        deliveryNotes.push(`${app.translate('Cargo provider name')}: ${integrationOrder.cargoProviderName}`);
    }
    if (!!integrationOrder.cargoTrackingNumber) {
        deliveryNotes.push(`${app.translate('Cargo tracking number')}: ${integrationOrder.cargoTrackingNumber}`);
    } else if (!!integrationOrder.cargoSenderNumber) {
        deliveryNotes.push(`${app.translate('Cargo sender number')}: ${integrationOrder.cargoSenderNumber}`);
    } else if (!!integrationOrder.packageNumber) {
        deliveryNotes.push(`${app.translate('Cargo tracking number')}: ${integrationOrder.packageNumber}`);
    }
    if (!!integrationOrder.cargoTrackingLink) {
        deliveryNotes.push(`${app.translate('Cargo tracking link')}: ${integrationOrder.cargoTrackingLink}`);
    }
    order.deliveryNote = deliveryNotes.join('\n');

    const contact = await app.collection('kernel.contacts').findOne({
        _id: deliveryAddressId,
        $select: ['defaultDeliveryOptionId']
    });


    if (Array.isArray(integrationCarriers) && integrationCarriers.length > 0) {

        const integrationCarrier = integrationCarriers.find(ic => ic.name === integrationOrder?.cargoProviderName);

        if (integrationCarrier) {
            const deliveryOption = store.deliveryOptions.find(sdo => sdo.integrationId === integrationCarrier?.id);
            order.carrierId = deliveryOption?.carrierId;
        } else {
            console.log('No matching integration carrier found for:', integrationOrder?.cargoProviderName);
        }
    }
    if (!_.isEmpty(customer.defaultDeliveryOptionId)) {
        order.carrierId = customer.defaultDeliveryOptionId;
    }
    if (_.isPlainObject(contact) && !_.isEmpty(contact.defaultDeliveryOptionId)) {
        order.carrierId = contact.defaultDeliveryOptionId;
    }
    if (!order.carrierId) {
        const storeDefaultDeliveryOption = store.deliveryOptions.find(sdo => sdo.id === store.defaultDeliveryOptionId);
        if (storeDefaultDeliveryOption) order.carrierId = storeDefaultDeliveryOption.carrierId;
    }

    // Calculate final discount.
    const totalIntegrationDiscountAmount = parseFloat(integrationOrder.totalDiscount || 0);
    const totalIntegrationOrderAmount = parseFloat(integrationOrder.totalPrice || 0);
    let discount = 0;
    if (!isNaN(totalIntegrationDiscountAmount) && !isNaN(totalIntegrationOrderAmount) && totalIntegrationOrderAmount > 0) {
        discount = (totalIntegrationDiscountAmount / totalIntegrationOrderAmount) * 100;
    }

    // Get items.
    order.items = await getOrderItems({
        app,
        company,
        store,
        now,
        user,
        integrationOrder,
        customer,
        invoiceAddressId,
        invoiceAddress,
        deliveryAddressId,
        deliveryAddress,
        order,
        discount
    });

    // Totals.
    let discountAmount = 0;
    let subTotal = 0;
    let subTotalAfterDiscount = 0;
    let taxTotal = 0;
    let grandTotal = 0;
    let appliedTaxes = [];
    order.items.forEach(row => {
        subTotal += app.round(row.total, 'total');

        if (_.isPlainObject(row.taxDetail)) {
            taxTotal += app.round(row.taxDetail.taxTotal, 'total');

            row.taxDetail.applied.forEach(tax => {
                const taxIndex = _.findIndex(appliedTaxes, t => t._id === tax._id);

                if (taxIndex !== -1) {
                    appliedTaxes[taxIndex].unAppliedAmount += app.round(tax.unAppliedAmount || 0, 'total');
                    appliedTaxes[taxIndex].appliedAmount += app.round(tax.appliedAmount || 0, 'total');
                } else {
                    tax.unAppliedAmount = app.round(tax.unAppliedAmount || 0, 'total');
                    tax.appliedAmount = app.round(tax.appliedAmount || 0, 'total');
                    appliedTaxes.push(_.cloneDeep(tax));
                }
            });
        }
    });
    grandTotal = app.round(subTotal + taxTotal, 'total');
    if (_.isNumber(discount) && subTotal > 0) {
        // Get discount amount.
        discountAmount = app.round((subTotal * discount) / 100, 'total');

        // Calculate new taxes and tax total.
        const ratio = discountAmount / subTotal;
        const payload = {items: []};
        order.items.forEach(row => {
            const rowDiscountAmount = app.round(ratio * row.total, 'total');
            const newRowTotal = app.round(row.total - rowDiscountAmount, 'total');
            const key = row.taxDetail.applied.map(t => t._id).join('');
            const existingIndex = _.findIndex(payload.items, i => i.key === key);
            if (existingIndex === -1) {
                payload.items.push({
                    taxId: row.taxId,
                    quantity: row.quantity || 1,
                    taxPayload: row.taxPayload,
                    amount: newRowTotal,
                    key
                });
            } else {
                payload.items[existingIndex].amount += newRowTotal;
                payload.items[existingIndex].quantity += row.quantity || 1;
            }
        });
        const result = await app.rpc('kernel.common.calculate-taxes', payload);
        taxTotal = 0;
        for (const r of result) {
            taxTotal += app.round(r.amount, 'total');
        }
        appliedTaxes = appliedTaxes.map(tax => {
            const newTaxResult = result.find(r => r.taxId === tax._id);

            tax.appliedAmount = app.round(newTaxResult.amount, 'total');

            return tax;
        });

        // Calculate subtotal after discount.
        subTotalAfterDiscount = app.round(subTotal - discountAmount, 'total');

        // Calculate new grand total.
        grandTotal = app.round(subTotal + taxTotal - discountAmount, 'total');
    }
    order.subTotal = app.round(subTotal, 'total');
    order.discount = discount;
    order.discountAmount = app.round(discountAmount, 'total');
    order.subTotalAfterDiscount = subTotalAfterDiscount;
    order.taxTotal = app.round(taxTotal, 'total');
    order.grandTotal = app.round(grandTotal, 'total');
    order.appliedTaxes = appliedTaxes;

    // Fix grand total.
    const orderPayloadGrandTotal = parseFloat(integrationOrder.totalPrice);
    if (orderPayloadGrandTotal !== order.grandTotal) {
        const diff = orderPayloadGrandTotal - order.grandTotal;

        order.rounding = app.round(diff, 'total');
        order.grandTotal = app.round(order.grandTotal + order.rounding, 'total');
    }

    // Initialize exchange rates.
    await (async () => {
        const currencies = await app.collection('kernel.currencies').find({
            $select: ['name']
        });
        const exchangeRates = [];
        const payloads = [];

        for (const currency of currencies) {
            if (currency.name === company.currency.name) {
                continue;
            }

            payloads.push({
                from: currency.name,
                to: company.currency.name,
                value: 1,
                options: {
                    date: order.orderDate
                }
            });
        }

        for (const payload of await app.rpc('kernel.common.convert-currencies', payloads)) {
            exchangeRates.push({
                currencyName: payload.from,
                rate: payload.rate
            });
        }

        order.exchangeRates = exchangeRates;

        const exchangeRatesMap = {};
        for (const exchangeRate of order.exchangeRates || []) {
            exchangeRatesMap[exchangeRate.currencyName] = exchangeRate.rate;
        }
        order.exchangeRatesMap = exchangeRatesMap;
    })();

    // Last-check.
    if ((await app.collection('sale.orders').count({code: order.code})) > 0) {
        return order;
    }

    order = await app.collection('sale.orders').create(order, {user, skipEvents: true});

    // Initialize profits.
    await app.rpc('sale.update-document-profits', {
        profitBase: app.setting('sale.profitBase'),
        documentId: order._id,
        documentCollection: 'sale.orders',
        currencyId: order.currencyId,
        currencyRate: order.currencyRate,
        date: order.orderDate,
        exchangeRates: order.exchangeRates ?? [],
        items: order.items.map(item => ({
            productId: item.productId,
            warehouseId: item.warehouseId,
            quantity: item.quantity,
            unitId: item.unitId,
            freight: item.freight || 0,
            totalSalesPrice: item.realTotal
        }))
    });

    // Initialize extras.
    const extra = {
        basePrice: 0,
        grossProfit: 0,
        profitRate: 0,
        profitMargin: 0,
        cashAmount: 0,
        cashInstallmentCount: 0,
        moneyTransferAmount: 0,
        moneyTransferInstallmentCount: 0,
        chequeAmount: 0,
        chequeInstallmentCount: 0,
        promissoryNoteAmount: 0,
        promissoryNoteInstallmentCount: 0,
        posAmount: 0,
        posInstallmentCount: 0,
        installmentCount: 0
    };
    const profit = await app.collection('sale.document-profits').findOne({
        documentId: order._id,
        documentCollection: 'sale.orders',
        $select: ['totalBasePrice', 'totalGrossProfit', 'profitRate', 'profitMargin']
    });
    if (_.isPlainObject(profit)) {
        extra.basePrice = profit.totalBasePrice;
        extra.grossProfit = profit.totalGrossProfit;
        extra.profitRate = profit.profitRate;
        extra.profitMargin = profit.profitMargin;
    }
    await app.collection('sale.orders').patch({_id: order._id}, {extra});

    try {
        await updateProductsSalesCount(app, store, order.items);
    } catch (error) {
        console.error(
            `An error occurred while updating product sales count! Store: ${store.name}, Error: `,
            error.message
        );
    }

    if (order.status === 'approved') {
        try {
            // Create external reservations.
            await (async () => {
                const items = order.items
                    .filter(item => item.productType !== 'service')
                    .map(item => {
                        const row = {};

                        row.productId = item.productId;
                        row.productType = item.productType;
                        row.date = item.scheduledDate;
                        row.warehouseId = item.warehouseId;
                        row.quantity = item.quantity;
                        row.unitId = item.unitId;
                        row.unitPrice = (item.realTotal / item.quantity) * (order.currencyRate || 1);

                        if (app.hasModule('pcm') && !!item.pcmHash) {
                            row.pcmModelId = item.pcmModelId;
                            row.pcmConfigurationId = item.pcmConfigurationId;
                            row.pcmHash = item.pcmHash;
                        }

                        return row;
                    });

                await app.rpc(
                    'inventory.create-external-reservations',
                    {
                        documentId: order._id,
                        documentCollection: 'sale.orders',
                        documentCode: order.code,
                        documentView: 'sale.sales.orders',
                        documentTitle: 'Sale Orders',
                        type: 'outgoing',
                        code: order.code,
                        partnerId: order.partnerId,
                        orderDate: order.orderDate,
                        exchangeRatesMap: order.exchangeRatesMap || {},
                        items,
                        checkProductStocks: false,
                        disableAdvancedDeliveryPlanning: true
                    },
                    {user}
                );
            })();

            if (store.updateMarketplaceOrderStatus) {
                try {
                    await ecommerceIntegrations.updateOrderStatus(
                        app,
                        order.storeId,
                        'sale.orders',
                        order._id,
                        'order-approved'
                    );
                } catch (error) {
                    console.log('Marketplace order status update error in [import-orders] ->', error);
                }
            }

            let isAdvancedDeliveryPlanningEnabled = !!app.setting('inventory.advancedDeliveryPlanning');
            if (isAdvancedDeliveryPlanningEnabled) {
                const warehouse = await app.collection('inventory.warehouses').findOne({
                    _id: store.warehouseId,
                    $select: ['_id', 'advancedDeliveryPlanning'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (!!warehouse && Array.isArray(warehouse.advancedDeliveryPlanning)) {
                    isAdvancedDeliveryPlanningEnabled = warehouse.advancedDeliveryPlanning.includes('sale');
                }
            }

            if (store.automaticDelivery && !isAdvancedDeliveryPlanningEnabled) {
                try {
                    await app.rpc('sale.create-deliveries', {orderIds: [order._id]});

                    if (store.automaticDeliveryConfirmation) {
                        const ecommerceOrder = await app.collection('sale.orders').findOne({
                            _id: order._id,
                            $select: ['transferIds'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });

                        if (
                            ecommerceOrder &&
                            Array.isArray(ecommerceOrder.transferIds) &&
                            ecommerceOrder.transferIds.length > 0
                        ) {
                            const transfers = await app.collection('inventory.transfers').find({
                                _id: {$in: ecommerceOrder.transferIds},
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            });

                            for (const transfer of transfers) {
                                await app.rpc(
                                    'inventory.transfers-save-transfer',
                                    {
                                        data: {
                                            ..._.omit(transfer, '_id'),
                                            shippingDocumentType: 'none',
                                            status: 'approved'
                                        },
                                        id: transfer._id
                                    },
                                    {user}
                                );
                            }

                            if (store.automaticShippingOrder) {
                                const approvedTransfers = await app.collection('inventory.transfers').find({
                                    _id: {$in: ecommerceOrder.transferIds},
                                    status: 'approved',
                                    $select: ['_id'],
                                    $disableActiveCheck: true,
                                    $disableSoftDelete: true
                                });

                                const packageTypes = await app.collection('logistics.package-types').find({
                                    carrierId: order.carrierId,
                                    $select: ['_id', 'isDefault'],
                                    $disableActiveCheck: true,
                                    $disableSoftDelete: true
                                });

                                for (const transfer of approvedTransfers) {
                                    await app.rpc('inventory.transfers-create-shipping-order', {
                                        transferId: transfer._id,
                                        carrierId: order.carrierId,
                                        packageTypeId:
                                            packageTypes.find(pkg => pkg.isDefault)?._id ?? packageTypes[0]?._id,
                                        cashOnDeliveryAmount: 0,
                                        shippingPaymentType: 'freight-prepaid',
                                        packagingType: 'package',
                                        weight: 1,
                                        volumetricWeight: 1,
                                        deliveryAddress: deliveryAddress
                                    });
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.log('Marketplace transfer creation and update error in [import-orders] ->', error);
                }
            }

            if (store.automaticInvoice) {
                await app.rpc('sale.create-invoices', {orderIds: [order._id]});

                if (integrationOrder.micro) {
                    const ecommerceOrder = await app.collection('sale.orders').findOne({
                        _id: order._id,
                        $select: ['relatedDocuments'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });

                    const rd = ecommerceOrder.relatedDocuments.find(
                        rd => rd.collection === 'accounting.customer-invoices'
                    );

                    if (rd && Array.isArray(rd.ids) && rd.ids.length > 0) {
                        await app.collection('accounting.customer-invoices').bulkWrite([
                            {
                                updateOne: {
                                    filter: {_id: rd.ids[0]},
                                    update: {
                                        $set: {
                                            ...(store.exportInvoiceScenario && {
                                                invoiceScenario: store.exportInvoiceScenario
                                            }),
                                            ...(store.exportEInvoiceTypeId && {
                                                eInvoiceTypeId: store.exportEInvoiceTypeId
                                            }),
                                            ...(store.exportEInvoiceTypeConditionCode && {
                                                eInvoiceTypeConditionCode: store.exportEInvoiceTypeConditionCode
                                            })
                                        }
                                    }
                                }
                            }
                        ]);
                    }
                }

                if (store.automaticInvoiceApproval) {
                    const invoice = await app.collection('accounting.customer-invoices').findOne({
                        reference: order.code
                    });

                    if (invoice) {
                        try {
                            if (!invoice.journalId) {
                                throw new Error(
                                    app.translate('{{label}} is required', {
                                        label: app.translate('Journal')
                                    })
                                );
                            }
                            if (!invoice.journalDescription) {
                                throw new Error(
                                    app.translate('{{label}} is required', {
                                        label: app.translate('Journal description')
                                    })
                                );
                            }
                            if (!invoice.accountingAccountId) {
                                throw new Error(
                                    app.translate('{{label}} is required', {
                                        label: app.translate('Account')
                                    })
                                );
                            }
                            if (invoice.documentNo) {
                                if (
                                    (await app.collection('accounting.customer-invoices').count({
                                        _id: {$ne: invoice._id},
                                        documentNo: invoice.documentNo,
                                        status: {$ne: 'canceled'}
                                    })) > 0
                                ) {
                                    throw new Error(app.translate('Document no must be unique!'));
                                }
                            }
                            if (invoice.invoiceScenario === 'normal' && app.setting('eops.isEInvoiceActivated')) {
                                throw new Error(
                                    app.translate(
                                        'You cannot approve the invoice with the normal invoice scenario when e-archive system is active!'
                                    )
                                );
                            }

                            const linkedDocumentIds = [];
                            for (const rd of invoice.relatedDocuments || []) {
                                if (rd.collection === 'sale.orders') {
                                    linkedDocumentIds.push(...rd.ids);
                                }
                            }
                            const limitResult = await app.rpc('finance.check-partner-limit', {
                                partnerId: invoice.partnerId,
                                currencyId: invoice.currencyId,
                                guaranteeId: invoice.guaranteeId,
                                amount: invoice.grandTotal,
                                linkedDocumentIds,
                                document: invoice.isReturn ? 'return-invoice' : 'invoice'
                            });
                            if (!!limitResult && limitResult.blacklist) {
                                throw new Error(
                                    app.translate(
                                        'The operation cannot be performed because the partner is in the blacklist!'
                                    )
                                );
                            }
                            if (!!limitResult && !limitResult.hasLimit && limitResult.reaction === 'block') {
                                throw new Error(limitResult.message);
                            }

                            await app.rpc(
                                'accounting.save-customer-invoice',
                                {
                                    data: {
                                        ..._.omit(invoice, ['_id', 'extra']),
                                        status: 'approved'
                                    },
                                    id: invoice._id
                                },
                                {user}
                            );
                        } catch (error) {
                            await app.collection('accounting.customer-invoices').bulkWrite([
                                {
                                    updateOne: {
                                        filter: {_id: invoice._id},
                                        update: {
                                            $set: {bulkApprovalError: error.message}
                                        }
                                    }
                                }
                            ]);
                        }
                    }
                }

                if (store.automaticInvoiceSending) {
                    const approvedInvoice = await app.collection('accounting.customer-invoices').findOne({
                        reference: order.code,
                        status: 'approved',
                        $select: ['_id', 'scopeRate', 'invoiceScenario']
                    });

                    if (approvedInvoice) {
                        try {
                            if (approvedInvoice.scopeRate === 0) {
                                throw new Error(
                                    app.translate('E-invoices cannot be sent for invoices with a zero scope ratio!')
                                );
                            }

                            if (approvedInvoice.invoiceScenario === 'e-archive-invoice') {
                                if (!store.eArchiveInvoiceNumberingId) {
                                    throw new Error(app.translate('E-Archive invoice numbering is not found!'));
                                }
                                if (!store.eArchiveInvoiceTemplateId) {
                                    throw new Error(app.translate('E-Archive invoice template is not found!'));
                                }

                                const eArchiveInvoice = await app.collection('eops.e-archive-invoices').findOne({
                                    invoiceId: approvedInvoice._id,
                                    $select: ['_id']
                                });

                                if (!eArchiveInvoice) {
                                    throw new Error(app.translate('E-archive Invoice is not found!'));
                                }

                                const invoiceDocumentNo = await app.rpc('kernel.common.request-number', {
                                    numberingId: store.eArchiveInvoiceNumberingId,
                                    date: now.toJSDate(),
                                    save: false
                                });

                                await app.rpc('eops.e-archive-invoices-send', {
                                    eArchiveInvoiceId: eArchiveInvoice._id,
                                    invoiceDocumentNo,
                                    invoiceIssueDate: now.toJSDate(),
                                    templateId: store.eArchiveInvoiceTemplateId,
                                    numberingId: store.eArchiveInvoiceNumberingId,
                                    saveNumbering: true
                                });
                            } else if (approvedInvoice.invoiceScenario !== 'normal') {
                                if (!store.eInvoiceNumberingId) {
                                    throw new Error(app.translate('E-Invoce numbering is not found!'));
                                }
                                if (!store.eInvoiceTemplateId) {
                                    throw new Error(app.translate('E-Invoice template is not found!'));
                                }

                                const eInvoice = await app.collection('eops.e-invoices').findOne({
                                    invoiceId: approvedInvoice._id,
                                    $select: ['_id', 'partnerTinIdentity']
                                });

                                if (!eInvoice) {
                                    throw new Error(app.translate('E-Invoice is not found!'));
                                }

                                let users = [];
                                if (
                                    approvedInvoice.invoiceScenario !== 'export-invoice' &&
                                    approvedInvoice.invoiceScenario !== 'passenger-invoice'
                                ) {
                                    users = await app.rpc('eops.e-invoices-get-users', eInvoice.partnerTinIdentity);
                                    users = _.orderBy(users, ['updatedAt'], ['desc']);
                                }

                                let allFailed = true;
                                let lastError = null;
                                for (let i = 0; i < users.length; i++) {
                                    try {
                                        const invoiceDocumentNo = await app.rpc('kernel.common.request-number', {
                                            numberingId: store.eInvoiceNumberingId,
                                            date: now.toJSDate(),
                                            save: false
                                        });

                                        await app.rpc('eops.e-invoices-send', {
                                            eInvoiceId: eInvoice._id,
                                            pbLabel: users[i]?.label || null,
                                            invoiceDocumentNo,
                                            invoiceIssueDate: now.toJSDate(),
                                            templateId: store.eInvoiceTemplateId,
                                            numberingId: store.eInvoiceNumberingId,
                                            saveNumbering: true
                                        });

                                        allFailed = false;
                                        break;
                                    } catch (error) {
                                        lastError = error.message;
                                    }
                                }

                                if (allFailed && lastError) {
                                    throw new Error(lastError);
                                }
                            }
                        } catch (error) {
                            await app.collection('accounting.customer-invoices').bulkWrite([
                                {
                                    updateOne: {
                                        filter: {_id: approvedInvoice._id},
                                        update: {
                                            $set: {bulkApprovalError: error.message}
                                        }
                                    }
                                }
                            ]);
                        }
                    }
                }
            }
        } catch (error) {
            console.log(error);
        }
    }

    return order;
}

async function getOrderItems({
    app,
    now,
    user,
    integrationOrder,
    customer,
    invoiceAddressId,
    invoiceAddress,
    deliveryAddressId,
    deliveryAddress,
    order,
    discount
}) {
    let items = [];

    for (const integrationOrderLine of integrationOrder.lines ?? []) {
        const barcode = integrationOrderLine.barcode;
        const product = await app.collection('inventory.products').findOne({
            $or: [{barcode}, {'barcodes.barcode': barcode}]
        });

        if (!product) {
            throw new Error(
                app.translate(
                    'The {{productName}} integration product could not be matched. No ERP product found for the relevant product! Barcode: {{barcode}}',
                    {
                        productName: integrationOrderLine.productName,
                        barcode
                    }
                )
            );
        }

        const matchedBarcodeInfo = product.barcodes.find(pb => pb.barcode === barcode) ?? {};

        let item = {
            id: Random.id(16),
            productId: product._id,
            productCode: product.code,
            productDefinition: product.definition,
            description: integrationOrderLine.productName || product.displayName,
            productType: 'stockable',
            barcode: matchedBarcodeInfo.barcode || barcode,
            scheduledDate: now.toJSDate(),
            branchId: order.branchId,
            warehouseId: order.warehouseId,
            quantity: integrationOrderLine.quantity ?? 1,
            unitId: matchedBarcodeInfo.unitId || product.baseUnitId,
            baseUnitId: product.baseUnitId,
            baseQuantity: integrationOrderLine.quantity,
            unitPrice: 0,
            grossUnitPrice: 0,
            discount: 0,
            unitPriceAfterDiscount: 0,
            grossUnitPriceAfterDiscount: 0,
            taxId: product.salesTaxId,
            taxTotal: 0,
            grossTotal: 0,
            stockQuantity: 0,
            orderedQuantity: 0,
            assignedQuantity: 0,
            availableQuantity: 0,
            warehouseStockQuantity: 0,
            warehouseOrderedQuantity: 0,
            warehouseAssignedQuantity: 0,
            warehouseAvailableQuantity: 0,
            total: 0,
            partnerId: customer._id,
            invoiceAddressId,
            invoiceAddress,
            deliveryAddressId,
            deliveryAddress
        };
        if (Array.isArray(product.nameTranslations) && product.nameTranslations.length > 0) {
            const nt = product.nameTranslations.find(nt => nt.languageId === customer.languageId);

            if (!!nt && !!nt.translation) {
                item.description = `${product.code} - ${nt.translation}`;
            }
        }

        if (item.unitId !== item.baseUnitId) {
            const unitRatio = (product.unitRatios ?? {})[item.unitId] ?? 1;

            item.baseQuantity = item.quantity * unitRatio;
        }

        // Vendor product.
        const pcItem = await app.collection('purchase.procurement-catalog').findOne({
            productId: product._id
        });
        if (!!pcItem) {
            item.vendorId = pcItem.vendorId;

            if (Array.isArray(pcItem.warehouses) && pcItem.warehouses.length > 0) {
                item.warehouseId = pcItem.warehouses[0].warehouseId;
            }
        }

        if (!!integrationOrder.micro) {
            integrationOrderLine.vatBaseAmount = 0;
        }

        if (integrationOrderLine.vatBaseAmount > 0) {
            item.unitPrice = app.round(
                (integrationOrderLine.price || 0) / (1 + integrationOrderLine.vatBaseAmount / 100),
                'unit-price'
            );
        } else {
            item.unitPrice = app.round(integrationOrderLine.price || 0, 'unit-price');
        }

        if (item.quantity > 0) {
            let totalDiscountAmount = parseFloat(integrationOrderLine.discount || 0);

            if (Array.isArray(integrationOrderLine.discountDetails) && integrationOrderLine.discountDetails.length > 0) {
                integrationOrderLine.discountDetails.forEach(discountDetail => {
                    const detailDiscountAmount = parseFloat(discountDetail.amount || 0);
                    if (!isNaN(detailDiscountAmount)) {
                        totalDiscountAmount += detailDiscountAmount;
                    }
                });
            }

            const rowTotal = (integrationOrderLine.price || 0) * item.quantity;

            if (totalDiscountAmount > 0 && rowTotal > 0) {
                item.discount = (totalDiscountAmount / rowTotal) * 100;
            } else {
                item.discount = 0;
            }
        } else {
            item.discount = 0;
        }

        // Stock quantity.
        const stockReport = await app.rpc('inventory.get-stock-report', {
            date: item.scheduledDate || app.datetime.local().toJSDate(),
            productId: item.productId
        });
        if (Array.isArray(stockReport) && stockReport.length > 0) {
            const r = stockReport[0];

            item.stockQuantity = r.stockQuantity;
            item.orderedQuantity = r.orderedQuantity;
            item.assignedQuantity = r.assignedQuantity;
            item.availableQuantity = r.availableQuantity;
        }
        const warehouseReport = await app.rpc('inventory.get-stock-report', {
            date: item.scheduledDate || app.datetime.local().toJSDate(),
            productId: item.productId,
            warehouseId: item.warehouseId
        });
        if (Array.isArray(warehouseReport) && warehouseReport.length > 0) {
            const r = warehouseReport[0];

            item.warehouseStockQuantity = r.stockQuantity;
            item.warehouseOrderedQuantity = r.orderedQuantity;
            item.warehouseAssignedQuantity = r.assignedQuantity;
            item.warehouseAvailableQuantity = r.availableQuantity;
        }

        item = await app.rpc(
            'sale.calculate-order-row-totals',
            {
                row: item,
                field: 'unitPrice',
                model: order
            },
            {user}
        );

        if (!(integrationOrderLine.vatBaseAmount > 0)) {
            const zeroTax = await app.collection('kernel.taxes').findOne({
                scope: 'sale',
                amount: 0,
                $select: ['_id']
            });

            if (!!zeroTax) {
                item = await app.rpc(
                    'sale.calculate-order-row-totals',
                    {
                        row: {
                            ...item,
                            taxId: zeroTax._id
                        },
                        field: 'taxId',
                        model: order
                    },
                    {user}
                );
            }
        }

        items.push(item);
    }

    return items;
}

async function getCustomer(app, company, user, store, integrationOrder) {
    const result = {
        customer: null,
        invoiceAddressId: null,
        invoiceAddress: null,
        deliveryAddressId: null,
        deliveryAddress: null,
        isNewCustomer: false
    };

    // Delivery address.
    const deliveryAddress = await normalizeAddress(app, integrationOrder.shipmentAddress);

    // Invoice address.
    const invoiceAddress = await normalizeAddress(app, integrationOrder.invoiceAddress);

    const numbering = await app.collection('kernel.numbering').findOne(
        {
            code: 'partnerCustomerNumbering',
            $select: ['_id']
        },
        {
            disableInUseCheck: true,
            disableActiveCheck: true,
            disableSoftDelete: true
        }
    );
    let customer = {};

    // Get delivery contact.
    let deliveryAddressContact = {};
    deliveryAddressContact.type = 'delivery-address';
    deliveryAddressContact.name = trim(integrationOrder.shipmentAddress.fullName || '');
    if (isArabic(deliveryAddressContact.name)) {
        deliveryAddressContact.name = arabicToEnName(deliveryAddressContact.name);
    }
    deliveryAddressContact.partnerId = '';
    deliveryAddressContact.partnerType = 'customer';
    if (!!integrationOrder.shipmentAddress.phone) {
        let phone = integrationOrder.shipmentAddress.phone.replace('+90', '');

        deliveryAddressContact.phone = phone;
        deliveryAddressContact.phoneCountryCode = '+90';
        deliveryAddressContact.phoneNumbers = [
            {
                type: 'work',
                phoneCode: '+90',
                countryCode: integrationOrder.shipmentAddress.countryCode,
                number: phone
            }
        ];
    } else if (Array.isArray(company.phoneNumbers) && company.phoneNumbers.length > 0) {
        deliveryAddressContact.phoneCountryCode = '+90';
        deliveryAddressContact.phone = company.phone.replace('+90', '').trim();
        deliveryAddressContact.phoneNumbers = company.phoneNumbers;
    }
    deliveryAddressContact.address = deliveryAddress;

    // Get invoice contact.
    let invoiceAddressContact = {};
    invoiceAddressContact.type = 'invoice-address';
    invoiceAddressContact.name = trim(integrationOrder.invoiceAddress.fullName || '');
    if (isArabic(invoiceAddressContact.name)) {
        invoiceAddressContact.name = arabicToEnName(invoiceAddressContact.name);
    }
    invoiceAddressContact.partnerId = '';
    invoiceAddressContact.partnerType = 'customer';
    if (!!integrationOrder.invoiceAddress.phone) {
        let phone = integrationOrder.invoiceAddress.phone.replace('+90', '');

        invoiceAddressContact.phone = phone;
        invoiceAddressContact.phoneCountryCode = '+90';
        invoiceAddressContact.phoneNumbers = [
            {
                type: 'work',
                phoneCode: '+90',
                countryCode: integrationOrder.invoiceAddress.countryCode,
                number: phone
            }
        ];
    } else if (Array.isArray(company.phoneNumbers) && company.phoneNumbers.length > 0) {
        invoiceAddressContact.phoneCountryCode = '+90';
        invoiceAddressContact.phone = company.phone.replace('+90', '').trim();
        invoiceAddressContact.phoneNumbers = company.phoneNumbers;
    }
    invoiceAddressContact.address = invoiceAddress;
    invoiceAddressContact.companyName = trim(integrationOrder.invoiceAddress.company || '');
    invoiceAddressContact.tin = trim(integrationOrder.invoiceAddress.taxNumber || '**********');
    invoiceAddressContact.taxDepartment = trim(integrationOrder.invoiceAddress.taxOffice || '');
    invoiceAddressContact.identity = trim(integrationOrder.tcIdentityNumber || '***********');
    if (typeof integrationOrder.identityNumber === 'string' && integrationOrder.identityNumber.trim().length > 0) {
        invoiceAddressContact.identity = integrationOrder.identityNumber.trim().slice(0, 11);
    }
    if (!!integrationOrder.commercial === true) {
        invoiceAddressContact.invoiceType = 'corporate';
        invoiceAddressContact.identity = '';

        if (!invoiceAddressContact.companyName) {
            invoiceAddressContact.companyName = invoiceAddressContact.name;
            if (isArabic(invoiceAddressContact.companyName)) {
                invoiceAddressContact.companyName = arabicToEnName(invoiceAddressContact.companyName);
            }
        }
    } else {
        invoiceAddressContact.invoiceType = 'individual';
        invoiceAddressContact.tin = '';
    }

    // Phone numbers.
    let phoneNumbers = [];
    let phone = null;
    let phoneCountryCode = null;
    if (!!invoiceAddressContact.phone) {
        phone = invoiceAddressContact.phone;
        phoneCountryCode = invoiceAddressContact.phoneCountryCode;
        phoneNumbers = invoiceAddressContact.phoneNumbers;
    }

    // General.
    customer.integrationId = integrationOrder.customerId.toString();
    customer.type = 'customer';
    customer.code = await app.rpc('kernel.common.request-number', {
        numberingId: numbering._id,
        save: true
    });
    // Extract customer name from available data
    customer.name = trim(integrationOrder.customerFullName || '');
    if (isArabic(customer.name)) {
        customer.name = arabicToEnName(customer.name);
    }

    // Split full name into first and last name
    const nameParts = customer.name.split(' ');
    customer.firstName = trim(nameParts[0] || '');
    customer.lastName = trim(nameParts.slice(1).join(' ') || '');

    if (isArabic(customer.firstName)) {
        customer.firstName = arabicToEnName(customer.firstName);
    }
    if (isArabic(customer.lastName)) {
        customer.lastName = arabicToEnName(customer.lastName);
    }
    customer.groupId = store.customerGroupId;
    customer.countryId = invoiceAddress.countryId;
    customer.timezone = app.config('app.timezone');
    customer.currencyId = store.currencyId;
    customer.languageId = store.languageId;
    customer.branchIds = [store.branchId];
    customer.invoiceScenario = 'e-archive-invoice';
    customer.address = invoiceAddress;
    customer.accountingAccountId = app.defaultAccountingAccount('domesticAccountsReceivableAccount', 'sale');
    customer.taxDepartment = invoiceAddressContact.taxOffice;
    if (invoiceAddressContact.invoiceType === 'corporate') {
        customer.isCompany = true;
        customer.legalName = invoiceAddressContact.companyName;
        if (isArabic(customer.legalName)) {
            customer.legalName = arabicToEnName(customer.legalName);
        }
        customer.invoiceScenario = 'commercial-invoice';
        customer.tin = '**********';
    } else {
        customer.isCompany = false;
        customer.legalName = customer.name;
        customer.invoiceScenario = 'e-archive-invoice';
        customer.identity = '***********';
    }

    // E-Invoice type.
    const eInvoiceTypes = await app.collection('eops.e-invoice-types').find({
        scenarios: customer.invoiceScenario,
        $select: ['_id']
    });
    if (eInvoiceTypes.length > 0) {
        customer.eInvoiceTypeId = eInvoiceTypes[0]._id;
    }

    // Phone number.
    if (!!phone) {
        customer.phone = phone;
        customer.phoneCountryCode = phoneCountryCode;
        customer.phoneNumbers = phoneNumbers;
    }

    // Default limit definition.
    const pld = await app.collection('finance.partner-limit-definitions').findOne({
        partnerType: 'customer',
        $or: [{partnerGroupId: {$exists: false}}, {partnerGroupId: {$eq: null}}, {partnerGroupId: customer.groupId}]
    });
    if (_.isPlainObject(pld)) {
        customer.enableLimitChecks = pld.enableLimitChecks;
        customer.limitControlDocument = pld.limitControlDocument;
        customer.limitOrderControl = pld.limitOrderControl;
        customer.limitInvoiceControl = pld.limitInvoiceControl;
        customer.openAccountLimit = pld.openAccountLimit;
        customer.totalLimit = pld.openAccountLimit;
    }

    if ((await app.collection('kernel.partners').count({code: customer.code})) > 0) {
        customer.code = microtime.now();
    }

    // Create customer.
    customer = await app.collection('kernel.partners').create(customer, {user});

    invoiceAddressContact.partnerId = customer._id;
    invoiceAddressContact = await app.collection('kernel.contacts').create(invoiceAddressContact, {user});

    deliveryAddressContact.partnerId = customer._id;
    deliveryAddressContact = await app.collection('kernel.contacts').create(deliveryAddressContact, {user});

    // Assign values.
    result.isNewCustomer = true;
    result.customer = customer;
    result.invoiceAddressId = invoiceAddressContact._id;
    result.invoiceAddress = invoiceAddressContact.address;
    result.deliveryAddressId = deliveryAddressContact._id;
    result.deliveryAddress = deliveryAddressContact.address;

    return result;
}

async function normalizeAddress(app, integrationAddress) {

    let country = await app.collection('kernel.countries').findOne({
        code: toUpper(integrationAddress.countryCode),
        $select: ['_id', 'name', 'addressFormat'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });

    // If country not found, try to find Turkey as default
    if (!country) {
        country = await app.collection('kernel.countries').findOne({
            code: 'TR',
            $select: ['_id', 'name', 'addressFormat'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
    }

    // If still no country found, throw error
    if (!country) {
        throw new Error(`No country found for code: ${integrationAddress.countryCode} and default Turkey not found`);
    }

    const normalized = {};

    let addressFormat = country.addressFormat;
    if (!addressFormat) {
        addressFormat = `{{street}}\n{{subDistrict}} {{district}} {{city}} {{postalCode}}\n{{country}}`;
    }

    normalized.countryId = trim(country._id);
    normalized.city = trim(integrationAddress.cityName || '');
    normalized.district = trim(integrationAddress.districtName || '');
    normalized.subDistrict = trim(integrationAddress.neighborhoodName || '');
    normalized.street = trim(integrationAddress.address1 || '');
    normalized.postalCode = trim((integrationAddress.postalCode || '').toString());
    normalized.apartmentNumber = '';
    normalized.doorNumber = '';
    normalized.address = '';

    let formatted = '';
    formatted = template(addressFormat, {
        subDistrict: normalized.subDistrict,
        street: normalized.street,
        apartmentNumber: normalized.apartmentNumber,
        doorNumber: normalized.doorNumber,
        postalCode: normalized.postalCode,
        district: normalized.district,
        city: normalized.city,
        country: country.name
    });
    formatted = formatted.trim();
    formatted = formatted.replace(' /', ' ');
    formatted = formatted.replace('/ ', ' ');
    formatted = formatted.replace('/,', ',');
    formatted = formatted.replace(',/', ',');
    formatted = formatted.replace('No: ,', ',');
    formatted = formatted
        .split(' ')
        .filter(part => !_.isEmpty(part.trim()) && part.trim() !== '/')
        .join(' ');
    formatted = formatted
        .split(',')
        .filter(part => !_.isEmpty(part.trim()) && part.trim() !== '/')
        .join(',');
    formatted = formatted.trim();
    if (formatted[0] === '/') {
        formatted = formatted.slice(1);
    }
    normalized.address = formatted;

    return normalized;
}
