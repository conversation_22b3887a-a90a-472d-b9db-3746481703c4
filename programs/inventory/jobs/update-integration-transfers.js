import _ from 'lodash';

export default {
    name: 'update-integration-transfers',
    repeatEvery: '*/1 * * * *',
    async action(app) {
        if ((await app.collection('inventory.integration-warehouses').count({})) < 1) {
            return;
        }

        const transfers = await app.collection('inventory.transfers').find({
            'integrationPayload.status': {$in: ['processing', 'failed']},
            'integrationPayload.integrationId': {$exists: true, $ne: null},
            'integrationPayload.orderId': {$exists: true, $ne: null},
            $select: ['_id', 'integrationPayload']
        });

        try {
            for (const transfer of transfers) {
                try {
                    await app.rpc('inventory.update-integration-transfer', {
                        documentId: transfer._id,
                        integrationId: transfer.integrationPayload?.integrationId,
                        orderId: transfer.integrationPayload?.orderId,
                        payload: transfer.integrationPayload
                    });
                } catch (error) {
                    console.error('Integration transfer update failed ->', error.message);
                }
            }
        } catch (error) {
            console.log(error.message);
        }
    }
};
